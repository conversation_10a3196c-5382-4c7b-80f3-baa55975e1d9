{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/common/layout.tsx", "../../src/components/common/protectedroute.tsx", "../../src/components/employee/jobinfostep.tsx", "../../src/components/employee/personalinfostep.tsx", "../../src/components/employee/reviewstep.tsx", "../../src/context/authcontext.tsx", "../../src/hooks/useemployees.ts", "../../src/pages/auth/login.tsx", "../../src/pages/dashboard/dashboard.tsx", "../../src/pages/employee/employeeform.tsx", "../../src/pages/employee/employeelist.tsx", "../../src/types/index.ts", "../../src/utils/i18n.ts"], "errors": true, "version": "5.8.3"}
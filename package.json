{"name": "v1", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^3.22.0", "@hookform/resolvers": "^5.1.1", "@mui/x-data-grid": "^8.8.0", "framer-motion": "^12.23.5", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.3", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}
import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Typography, Container, Button, Box, CircularProgress } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { AuthProvider } from './context/AuthContext';
import ProtectedRoute from './components/common/ProtectedRoute';
import SimpleLayout from './components/common/SimpleLayout';
import Login from './pages/auth/Login';
import Dashboard from './pages/dashboard/Dashboard';
import EmployeeList from './pages/employee/EmployeeList';
import EmployeeForm from './pages/employee/EmployeeForm';
import './utils/i18n';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
  },
});

const HomePage = () => {
  const { t, i18n } = useTranslation();

  const toggleLanguage = () => {
    const newLang = i18n.language === 'ar' ? 'en' : 'ar';
    i18n.changeLanguage(newLang);
    document.dir = newLang === 'ar' ? 'rtl' : 'ltr';
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom>
        {t('employee.title')}
      </Typography>
      <Typography variant="body1" paragraph>
        i18n is working! Current language: {i18n.language}
      </Typography>
      <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
        <Button variant="contained" color="primary" component={Link} to="/login">
          {t('common.login')}
        </Button>
        <Button variant="outlined" component={Link} to="/dashboard">
          {t('dashboard.title')}
        </Button>
      </Box>
      <Button variant="outlined" onClick={toggleLanguage}>
        Switch to {i18n.language === 'ar' ? 'English' : 'العربية'}
      </Button>
    </Container>
  );
};

const DashboardPage = () => {
  const { t } = useTranslation();
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        {t('dashboard.title')}
      </Typography>
      <Typography variant="body1" paragraph>
        {t('dashboard.welcome')}! You are logged in.
      </Typography>
      <Button variant="contained" component={Link} to="/">
        Back to Home
      </Button>
    </Container>
  );
};

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Suspense fallback={
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
          <CircularProgress />
        </Box>
      }>
        <AuthProvider>
          <Router>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/login" element={<Login />} />
              <Route path="/" element={
                <ProtectedRoute>
                  <SimpleLayout />
                </ProtectedRoute>
              }>
                <Route path="dashboard" element={<Dashboard />} />
                <Route path="employees" element={<EmployeeList />} />
                <Route path="employees/new" element={<EmployeeForm />} />
                <Route path="employees/edit/:id" element={<EmployeeForm />} />
              </Route>
            </Routes>
          </Router>
        </AuthProvider>
      </Suspense>
    </ThemeProvider>
  );
}

export default App;

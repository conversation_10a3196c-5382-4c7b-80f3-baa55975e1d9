// Mock API for Employee Management System
// This simulates real API calls with localStorage as the database

import type {
  Employee,
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  User,
  LoginRequest
} from '../types';

interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

// Mock data
const MOCK_EMPLOYEES: Employee[] = [
  {
    id: '1',
    firstName: 'أحمد',
    lastName: 'محمد',
    email: '<EMAIL>',
    phone: '+966501234567',
    position: 'مطور برمجيات',
    department: 'تقنية المعلومات',
    salary: 8000,
    hireDate: '2023-01-15',
    status: 'active',
    createdAt: '2023-01-15T10:00:00Z',
    updatedAt: '2023-01-15T10:00:00Z'
  },
  {
    id: '2',
    firstName: 'فاطمة',
    lastName: 'أحمد',
    email: '<EMAIL>',
    phone: '+966502345678',
    position: 'محاسب',
    department: 'المالية',
    salary: 7000,
    hireDate: '2023-02-01',
    status: 'active',
    createdAt: '2023-02-01T10:00:00Z',
    updatedAt: '2023-02-01T10:00:00Z'
  },
  {
    id: '3',
    firstName: 'محمد',
    lastName: 'علي',
    email: '<EMAIL>',
    phone: '+966503456789',
    position: 'مدير مشروع',
    department: 'إدارة المشاريع',
    salary: 12000,
    hireDate: '2022-11-10',
    status: 'active',
    createdAt: '2022-11-10T10:00:00Z',
    updatedAt: '2022-11-10T10:00:00Z'
  },
  {
    id: '4',
    firstName: 'سارة',
    lastName: 'خالد',
    email: '<EMAIL>',
    phone: '+966504567890',
    position: 'مصمم جرافيك',
    department: 'التسويق',
    salary: 6500,
    hireDate: '2023-03-20',
    status: 'active',
    createdAt: '2023-03-20T10:00:00Z',
    updatedAt: '2023-03-20T10:00:00Z'
  },
  {
    id: '5',
    firstName: 'عبدالله',
    lastName: 'حسن',
    email: '<EMAIL>',
    phone: '+966505678901',
    position: 'محلل أعمال',
    department: 'تقنية المعلومات',
    salary: 7500,
    hireDate: '2023-04-05',
    status: 'inactive',
    createdAt: '2023-04-05T10:00:00Z',
    updatedAt: '2023-04-05T10:00:00Z'
  }
];

const MOCK_USER: User = {
  id: '1',
  email: '<EMAIL>',
  name: 'مدير النظام',
  role: 'admin'
};

// Utility functions
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const generateId = () => Date.now().toString();

const getStoredEmployees = (): Employee[] => {
  const stored = localStorage.getItem('employees');
  if (stored) {
    return JSON.parse(stored);
  }
  // Initialize with mock data if no data exists
  localStorage.setItem('employees', JSON.stringify(MOCK_EMPLOYEES));
  return MOCK_EMPLOYEES;
};

const saveEmployees = (employees: Employee[]) => {
  localStorage.setItem('employees', JSON.stringify(employees));
};

// API Functions
export const authAPI = {
  async login(credentials: LoginRequest): Promise<ApiResponse<{ user: User; token: string }>> {
    await delay(1000); // Simulate network delay
    
    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
      const token = 'mock-jwt-token-' + Date.now();
      localStorage.setItem('auth_token', token);
      localStorage.setItem('user', JSON.stringify(MOCK_USER));
      
      return {
        data: { user: MOCK_USER, token },
        message: 'Login successful',
        success: true
      };
    }
    
    throw new Error('Invalid credentials');
  },

  async logout(): Promise<ApiResponse<null>> {
    await delay(500);
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
    
    return {
      data: null,
      message: 'Logout successful',
      success: true
    };
  },

  getCurrentUser(): User | null {
    const stored = localStorage.getItem('user');
    return stored ? JSON.parse(stored) : null;
  },

  isAuthenticated(): boolean {
    return !!localStorage.getItem('auth_token');
  }
};

export const employeeAPI = {
  async getEmployees(): Promise<ApiResponse<Employee[]>> {
    await delay(800);
    const employees = getStoredEmployees();
    
    return {
      data: employees,
      message: 'Employees retrieved successfully',
      success: true
    };
  },

  async getEmployee(id: string): Promise<ApiResponse<Employee | null>> {
    await delay(500);
    const employees = getStoredEmployees();
    const employee = employees.find(emp => emp.id === id);
    
    return {
      data: employee || null,
      message: employee ? 'Employee found' : 'Employee not found',
      success: !!employee
    };
  },

  async createEmployee(employeeData: CreateEmployeeRequest): Promise<ApiResponse<Employee>> {
    await delay(1000);
    const employees = getStoredEmployees();
    
    const newEmployee: Employee = {
      ...employeeData,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    employees.push(newEmployee);
    saveEmployees(employees);
    
    return {
      data: newEmployee,
      message: 'Employee created successfully',
      success: true
    };
  },

  async updateEmployee(employeeData: UpdateEmployeeRequest): Promise<ApiResponse<Employee>> {
    await delay(1000);
    const employees = getStoredEmployees();
    const index = employees.findIndex(emp => emp.id === employeeData.id);
    
    if (index === -1) {
      throw new Error('Employee not found');
    }
    
    const updatedEmployee: Employee = {
      ...employees[index],
      ...employeeData,
      updatedAt: new Date().toISOString()
    };
    
    employees[index] = updatedEmployee;
    saveEmployees(employees);
    
    return {
      data: updatedEmployee,
      message: 'Employee updated successfully',
      success: true
    };
  },

  async deleteEmployee(id: string): Promise<ApiResponse<null>> {
    await delay(800);
    const employees = getStoredEmployees();
    const index = employees.findIndex(emp => emp.id === id);
    
    if (index === -1) {
      throw new Error('Employee not found');
    }
    
    employees.splice(index, 1);
    saveEmployees(employees);
    
    return {
      data: null,
      message: 'Employee deleted successfully',
      success: true
    };
  }
};

export const dashboardAPI = {
  async getStats(): Promise<ApiResponse<{
    totalEmployees: number;
    activeEmployees: number;
    departments: number;
    recentHires: number;
  }>> {
    await delay(600);
    const employees = getStoredEmployees();
    
    const stats = {
      totalEmployees: employees.length,
      activeEmployees: employees.filter(emp => emp.status === 'active').length,
      departments: [...new Set(employees.map(emp => emp.department))].length,
      recentHires: employees.filter(emp => {
        const hireDate = new Date(emp.hireDate);
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return hireDate >= thirtyDaysAgo;
      }).length
    };
    
    return {
      data: stats,
      message: 'Dashboard stats retrieved successfully',
      success: true
    };
  }
};



import React from 'react';
import { useNavigate, Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  Button,
  Container,
  Paper
} from '@mui/material';
import { useAuth } from '../../context/AuthContext';

const SimpleLayout: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleLanguage = () => {
    const newLang = i18n.language === 'ar' ? 'en' : 'ar';
    i18n.changeLanguage(newLang);
    document.dir = newLang === 'ar' ? 'rtl' : 'ltr';
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {t('employee.title')}
          </Typography>
          <Button color="inherit" onClick={() => navigate('/dashboard')}>
            {t('dashboard.title')}
          </Button>
          <Button color="inherit" onClick={() => navigate('/employees')}>
            {t('employee.list')}
          </Button>
          <Button color="inherit" onClick={toggleLanguage}>
            {i18n.language === 'ar' ? 'EN' : 'AR'}
          </Button>
          <Button color="inherit" onClick={handleLogout}>
            {t('common.logout')}
          </Button>
        </Toolbar>
      </AppBar>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Paper sx={{ p: 3 }}>
          <Outlet />
        </Paper>
      </Container>
    </Box>
  );
};

export default SimpleLayout;

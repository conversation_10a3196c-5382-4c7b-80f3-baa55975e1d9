import React, { useState } from 'react';
import { useNavigate, Outlet, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  Typography,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Divider,
  Fade,
  useTheme,
  useMediaQuery,
  Chip,
  Badge
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Language as LanguageIcon,
  Logout as LogoutIcon,
  AccountCircle,
  Menu as MenuIcon,
  PersonAdd as PersonAddIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';

const drawerWidth = 240;

const SimpleLayout: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleLanguage = () => {
    const newLang = i18n.language === 'ar' ? 'en' : 'ar';
    i18n.changeLanguage(newLang);
    document.dir = newLang === 'ar' ? 'rtl' : 'ltr';
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const menuItems = [
    {
      text: t('dashboard.title'),
      icon: <DashboardIcon />,
      path: '/dashboard',
      color: 'rgb(196, 20, 20)',
      badge: null
    },
    {
      text: t('employee.list'),
      icon: <PeopleIcon />,
      path: '/employees',
      color: 'rgb(196, 20, 20)',
      badge: '24'
    },
    {
      text: t('employee.add'),
      icon: <PersonAddIcon />,
      path: '/employees/new',
      color: '#ca8a04',
      badge: null
    },
    {
      text: 'Analytics',
      icon: <AnalyticsIcon />,
      path: '/analytics',
      color: '#14b8a6',
      badge: null
    },
    {
      text: 'Settings',
      icon: <SettingsIcon />,
      path: '/settings',
      color: '#3b82f6',
      badge: null
    }
  ];

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Sidebar Header */}
      <Box
        sx={{
          p: 2,
          background: 'linear-gradient(135deg, rgb(196, 20, 20) 0%, #b71c1c 100%)',
          color: 'white',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
          <Avatar
            sx={{
              width: 40,
              height: 40,
              mr: 1.5,
              background: 'rgba(255, 255, 255, 0.2)',
              backdropFilter: 'blur(10px)',
            }}
          >
            <DashboardIcon sx={{ fontSize: 20 }} />
          </Avatar>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600, lineHeight: 1.2, fontSize: '1rem' }}>
              Admin123
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9, fontSize: '0.75rem' }}>
              {t('employee.title')}
            </Typography>
          </Box>
        </Box>
        <Chip
          label={user?.name || 'Administrator'}
          size="small"
          sx={{
            background: 'rgba(255, 255, 255, 0.2)',
            color: 'white',
            fontWeight: 500,
            fontSize: '0.75rem',
            height: 24,
          }}
        />
      </Box>

      {/* Navigation Menu */}
      <Box sx={{ flexGrow: 1, p: 1.5 }}>
        <List sx={{ p: 0 }}>
          {menuItems.map((item, index) => (
            <ListItem key={item.path} disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                onClick={() => navigate(item.path)}
                selected={location.pathname === item.path}
                sx={{
                  borderRadius: 1.5,
                  py: 1,
                  px: 1.5,
                  minHeight: 40,
                  transition: 'all 0.2s ease',
                  '&.Mui-selected': {
                    background: 'linear-gradient(135deg, rgb(196, 20, 20) 0%, #b71c1c 100%)',
                    color: 'white',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #b71c1c 0%, rgb(196, 20, 20) 100%)',
                    },
                  },
                  '&:hover': {
                    background: 'rgba(196, 20, 20, 0.08)',
                    transform: i18n.language === 'ar' ? 'translateX(-2px)' : 'translateX(2px)',
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    color: location.pathname === item.path ? 'white' : item.color,
                    minWidth: 32,
                    '& .MuiSvgIcon-root': {
                      fontSize: '1.25rem',
                    },
                  }}
                >
                  {item.badge ? (
                    <Badge badgeContent={item.badge} color="error" sx={{ '& .MuiBadge-badge': { fontSize: '0.625rem', minWidth: 16, height: 16 } }}>
                      {item.icon}
                    </Badge>
                  ) : (
                    item.icon
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  primaryTypographyProps={{
                    fontWeight: location.pathname === item.path ? 600 : 500,
                    fontSize: '0.875rem',
                  }}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>

      <Divider sx={{ mx: 2 }} />

      {/* Bottom Actions */}
      <Box sx={{ p: 1.5 }}>
        <ListItem disablePadding sx={{ mb: 0.5 }}>
          <ListItemButton
            onClick={toggleLanguage}
            sx={{
              borderRadius: 1.5,
              py: 1,
              px: 1.5,
              minHeight: 40,
              '&:hover': {
                background: 'rgba(196, 20, 20, 0.08)',
              },
            }}
          >
            <ListItemIcon sx={{ color: 'rgb(196, 20, 20)', minWidth: 32, '& .MuiSvgIcon-root': { fontSize: '1.25rem' } }}>
              <LanguageIcon />
            </ListItemIcon>
            <ListItemText
              primary={i18n.language === 'ar' ? 'English' : 'العربية'}
              primaryTypographyProps={{ fontSize: '0.875rem' }}
            />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding>
          <ListItemButton
            onClick={handleLogout}
            sx={{
              borderRadius: 1.5,
              py: 1,
              px: 1.5,
              minHeight: 40,
              color: 'error.main',
              '&:hover': {
                background: 'rgba(244, 67, 54, 0.08)',
              },
            }}
          >
            <ListItemIcon sx={{ color: 'error.main', minWidth: 32, '& .MuiSvgIcon-root': { fontSize: '1.25rem' } }}>
              <LogoutIcon />
            </ListItemIcon>
            <ListItemText
              primary={t('common.logout')}
              primaryTypographyProps={{ fontSize: '0.875rem' }}
            />
          </ListItemButton>
        </ListItem>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: i18n.language === 'ar' ? 0 : { md: `${drawerWidth}px` },
          mr: i18n.language === 'ar' ? { md: `${drawerWidth}px` } : 0,
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(255, 255, 255, 0.2)',
          color: theme.palette.text.primary,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, fontWeight: 600 }}>
            {menuItems.find(item => item.path === location.pathname)?.text || t('dashboard.title')}
          </Typography>
          <IconButton
            onClick={() => {}}
            sx={{
              mr: 1,
              background: 'rgba(196, 20, 20, 0.1)',
              '&:hover': {
                background: 'rgba(196, 20, 20, 0.2)',
              },
            }}
          >
            <Badge badgeContent={3} color="error">
              <NotificationsIcon />
            </Badge>
          </IconButton>
          <IconButton
            onClick={handleMenu}
            sx={{
              background: 'rgba(196, 20, 20, 0.1)',
              '&:hover': {
                background: 'rgba(196, 20, 20, 0.2)',
              },
            }}
          >
            <AccountCircle />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            PaperProps={{
              sx: {
                borderRadius: 2,
                mt: 1,
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
              },
            }}
          >
            <MenuItem disabled>
              <Typography variant="body2" color="text.secondary">
                {user?.name}
              </Typography>
            </MenuItem>
            <MenuItem onClick={handleLogout}>
              <LogoutIcon sx={{ mr: 1 }} />
              {t('common.logout')}
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(20px)',
              border: 'none',
              right: i18n.language === 'ar' ? 0 : 'auto',
              left: i18n.language === 'ar' ? 'auto' : 0,
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          anchor={i18n.language === 'ar' ? 'right' : 'left'}
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(20px)',
              border: 'none',
              boxShadow: i18n.language === 'ar'
                ? '-4px 0 20px rgba(0, 0, 0, 0.1)'
                : '4px 0 20px rgba(0, 0, 0, 0.1)',
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          minHeight: '100vh',
          background: 'rgb(246, 247, 248)',
          ml: i18n.language === 'ar' ? 0 : { md: `${drawerWidth}px` },
          mr: i18n.language === 'ar' ? { md: `${drawerWidth}px` } : 0,
        }}
      >
        <Toolbar />
        <Box sx={{ p: { xs: 2, sm: 2, md: 3 } }}>
          <Fade in={true} timeout={600}>
            <Box
              sx={{
                background: 'white',
                borderRadius: 2,
                border: '1px solid rgba(0, 0, 0, 0.08)',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
                p: { xs: 2, sm: 3 },
                minHeight: 'calc(100vh - 100px)',
              }}
              className="fade-in"
            >
              <Outlet />
            </Box>
          </Fade>
        </Box>
      </Box>
    </Box>
  );
};

export default SimpleLayout;

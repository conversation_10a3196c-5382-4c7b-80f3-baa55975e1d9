import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';

interface JobInfoStepProps {
  formData: any;
  onChange: (field: string, value: any) => void;
  errors: any;
}

const JobInfoStep: React.FC<JobInfoStepProps> = ({ formData, onChange, errors }) => {
  const { t } = useTranslation();

  const departments = [
    'تقنية المعلومات',
    'المالية',
    'الموارد البشرية',
    'التسويق',
    'إدارة المشاريع',
    'المبيعات'
  ];

  const positions = [
    'مطور برمجيات',
    'محاسب',
    'مدير مشروع',
    'مصمم جرافيك',
    'محلل أعمال',
    'مدير موارد بشرية',
    'أخصائي تسويق',
    'مندوب مبيعات'
  ];

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth error={!!errors.position}>
          <InputLabel>{t('employee.position')}</InputLabel>
          <Select
            value={formData.position || ''}
            onChange={(e) => onChange('position', e.target.value)}
            label={t('employee.position')}
          >
            {positions.map((position) => (
              <MenuItem key={position} value={position}>
                {position}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth error={!!errors.department}>
          <InputLabel>{t('employee.department')}</InputLabel>
          <Select
            value={formData.department || ''}
            onChange={(e) => onChange('department', e.target.value)}
            label={t('employee.department')}
          >
            {departments.map((department) => (
              <MenuItem key={department} value={department}>
                {department}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          type="number"
          label={t('employee.salary')}
          value={formData.salary || ''}
          onChange={(e) => onChange('salary', e.target.value)}
          error={!!errors.salary}
          helperText={errors.salary?.message}
          InputProps={{
            inputProps: { min: 0 }
          }}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          type="date"
          label={t('employee.hireDate')}
          value={formData.hireDate || ''}
          onChange={(e) => onChange('hireDate', e.target.value)}
          error={!!errors.hireDate}
          helperText={errors.hireDate?.message}
          InputLabelProps={{
            shrink: true,
          }}
        />
      </Grid>
    </Grid>
  );
};

export default JobInfoStep;

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import {
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText
} from '@mui/material';

interface EmployeeFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  department: string;
  salary: string;
  hireDate: string;
  status: 'active' | 'inactive';
}

interface JobInfoStepProps {
  control: Control<EmployeeFormData>;
  errors: FieldErrors<EmployeeFormData>;
}

const JobInfoStep: React.FC<JobInfoStepProps> = ({ control, errors }) => {
  const { t } = useTranslation();

  const departments = [
    'تقنية المعلومات',
    'المالية',
    'الموارد البشرية',
    'التسويق',
    'إدارة المشاريع',
    'المبيعات'
  ];

  const positions = [
    'مطور برمجيات',
    'محاسب',
    'مدير مشروع',
    'مصمم جرافيك',
    'محلل أعمال',
    'مدير موارد بشرية',
    'أخصائي تسويق',
    'مندوب مبيعات'
  ];

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={6}>
        <Controller
          name="position"
          control={control}
          render={({ field }) => (
            <FormControl fullWidth error={!!errors.position}>
              <InputLabel>{t('employee.position')}</InputLabel>
              <Select {...field} label={t('employee.position')}>
                {positions.map((position) => (
                  <MenuItem key={position} value={position}>
                    {position}
                  </MenuItem>
                ))}
              </Select>
              {errors.position && (
                <FormHelperText>{errors.position.message}</FormHelperText>
              )}
            </FormControl>
          )}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <Controller
          name="department"
          control={control}
          render={({ field }) => (
            <FormControl fullWidth error={!!errors.department}>
              <InputLabel>{t('employee.department')}</InputLabel>
              <Select {...field} label={t('employee.department')}>
                {departments.map((department) => (
                  <MenuItem key={department} value={department}>
                    {department}
                  </MenuItem>
                ))}
              </Select>
              {errors.department && (
                <FormHelperText>{errors.department.message}</FormHelperText>
              )}
            </FormControl>
          )}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <Controller
          name="salary"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              type="number"
              label={t('employee.salary')}
              error={!!errors.salary}
              helperText={errors.salary?.message}
              InputProps={{
                inputProps: { min: 0 }
              }}
            />
          )}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <Controller
          name="hireDate"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              type="date"
              label={t('employee.hireDate')}
              error={!!errors.hireDate}
              helperText={errors.hireDate?.message}
              InputLabelProps={{
                shrink: true,
              }}
            />
          )}
        />
      </Grid>
    </Grid>
  );
};

export default JobInfoStep;

import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';

interface PersonalInfoStepProps {
  formData: any;
  onChange: (field: string, value: any) => void;
  errors: any;
}

const PersonalInfoStep: React.FC<PersonalInfoStepProps> = ({ formData, onChange, errors }) => {
  const { t } = useTranslation();

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label={t('employee.firstName')}
          value={formData.firstName || ''}
          onChange={(e) => onChange('firstName', e.target.value)}
          error={!!errors.firstName}
          helperText={errors.firstName?.message}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label={t('employee.lastName')}
          value={formData.lastName || ''}
          onChange={(e) => onChange('lastName', e.target.value)}
          error={!!errors.lastName}
          helperText={errors.lastName?.message}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          type="email"
          label={t('employee.email')}
          value={formData.email || ''}
          onChange={(e) => onChange('email', e.target.value)}
          error={!!errors.email}
          helperText={errors.email?.message}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label={t('employee.phone')}
          value={formData.phone || ''}
          onChange={(e) => onChange('phone', e.target.value)}
          error={!!errors.phone}
          helperText={errors.phone?.message}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth error={!!errors.status}>
          <InputLabel>{t('employee.status')}</InputLabel>
          <Select
            value={formData.status || 'active'}
            onChange={(e) => onChange('status', e.target.value)}
            label={t('employee.status')}
          >
            <MenuItem value="active">{t('employee.active')}</MenuItem>
            <MenuItem value="inactive">{t('employee.inactive')}</MenuItem>
          </Select>
        </FormControl>
      </Grid>
    </Grid>
  );
};

export default PersonalInfoStep;

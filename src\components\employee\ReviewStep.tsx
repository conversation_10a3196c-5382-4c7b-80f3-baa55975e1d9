import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Grid,
  Typography,
  Paper,
  Box,
  Divider,
  Chip
} from '@mui/material';

interface EmployeeFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  department: string;
  salary: string;
  hireDate: string;
  status: 'active' | 'inactive';
}

interface ReviewStepProps {
  formData: EmployeeFormData;
}

const ReviewStep: React.FC<ReviewStepProps> = ({ formData }) => {
  const { t } = useTranslation();

  const InfoSection: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
    <Paper sx={{ p: 3, mb: 2 }}>
      <Typography variant="h6" gutterBottom color="primary">
        {title}
      </Typography>
      <Divider sx={{ mb: 2 }} />
      {children}
    </Paper>
  );

  const InfoRow: React.FC<{ label: string; value: string | number }> = ({ label, value }) => (
    <Box display="flex" justifyContent="space-between" alignItems="center" py={1}>
      <Typography variant="body2" color="text.secondary">
        {label}:
      </Typography>
      <Typography variant="body1" fontWeight="medium">
        {value}
      </Typography>
    </Box>
  );

  return (
    <Box>
      <Typography variant="h5" gutterBottom align="center" color="primary">
        {t('employee.details')}
      </Typography>
      
      <InfoSection title={t('employee.personalInfo')}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <InfoRow 
              label={t('employee.firstName')} 
              value={formData.firstName} 
            />
          </Grid>
          <Grid item xs={12}>
            <InfoRow 
              label={t('employee.lastName')} 
              value={formData.lastName} 
            />
          </Grid>
          <Grid item xs={12}>
            <InfoRow 
              label={t('employee.email')} 
              value={formData.email} 
            />
          </Grid>
          <Grid item xs={12}>
            <InfoRow 
              label={t('employee.phone')} 
              value={formData.phone} 
            />
          </Grid>
          <Grid item xs={12}>
            <Box display="flex" justifyContent="space-between" alignItems="center" py={1}>
              <Typography variant="body2" color="text.secondary">
                {t('employee.status')}:
              </Typography>
              <Chip
                label={t(`employee.${formData.status}`)}
                color={formData.status === 'active' ? 'success' : 'default'}
                size="small"
              />
            </Box>
          </Grid>
        </Grid>
      </InfoSection>

      <InfoSection title={t('employee.jobInfo')}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <InfoRow 
              label={t('employee.position')} 
              value={formData.position} 
            />
          </Grid>
          <Grid item xs={12}>
            <InfoRow 
              label={t('employee.department')} 
              value={formData.department} 
            />
          </Grid>
          <Grid item xs={12}>
            <InfoRow 
              label={t('employee.salary')} 
              value={`${formData.salary} SAR`} 
            />
          </Grid>
          <Grid item xs={12}>
            <InfoRow 
              label={t('employee.hireDate')} 
              value={formData.hireDate} 
            />
          </Grid>
        </Grid>
      </InfoSection>
    </Box>
  );
};

export default ReviewStep;

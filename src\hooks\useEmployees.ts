import { useState, useEffect } from 'react';
import { employeeAPI } from '../api';
import type { Employee, CreateEmployeeRequest, UpdateEmployeeRequest } from '../types';

export const useEmployees = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadEmployees();
  }, []);

  const loadEmployees = async () => {
    setLoading(true);
    try {
      const response = await employeeAPI.getEmployees();
      if (response.success) {
        setEmployees(response.data);
      }
    } catch (error) {
      console.error('Failed to load employees:', error);
    } finally {
      setLoading(false);
    }
  };

  const createEmployee = async (employeeData: CreateEmployeeRequest): Promise<Employee> => {
    setLoading(true);
    try {
      const response = await employeeAPI.createEmployee(employeeData);
      if (response.success) {
        setEmployees(prev => [...prev, response.data]);
        return response.data;
      }
      throw new Error(response.message);
    } finally {
      setLoading(false);
    }
  };

  const updateEmployee = async (employeeData: UpdateEmployeeRequest): Promise<Employee> => {
    setLoading(true);
    try {
      const response = await employeeAPI.updateEmployee(employeeData);
      if (response.success) {
        setEmployees(prev => prev.map(emp =>
          emp.id === employeeData.id ? response.data : emp
        ));
        return response.data;
      }
      throw new Error(response.message);
    } finally {
      setLoading(false);
    }
  };

  const deleteEmployee = async (id: string): Promise<void> => {
    setLoading(true);
    try {
      const response = await employeeAPI.deleteEmployee(id);
      if (response.success) {
        setEmployees(prev => prev.filter(emp => emp.id !== id));
      } else {
        throw new Error(response.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const getEmployee = (id: string): Employee | undefined => {
    return employees.find(emp => emp.id === id);
  };

  return {
    employees,
    loading,
    createEmployee,
    updateEmployee,
    deleteEmployee,
    getEmployee,
    refreshEmployees: loadEmployees
  };
};

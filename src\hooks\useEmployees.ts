import { useState, useEffect } from 'react';
import { Employee, CreateEmployeeRequest, UpdateEmployeeRequest } from '../types';

// Mock data for employees
const mockEmployees: Employee[] = [
  {
    id: '1',
    firstName: 'أحمد',
    lastName: 'محمد',
    email: '<EMAIL>',
    phone: '+966501234567',
    position: 'مطور برمجيات',
    department: 'تقنية المعلومات',
    salary: 8000,
    hireDate: '2023-01-15',
    status: 'active',
    createdAt: '2023-01-15T10:00:00Z',
    updatedAt: '2023-01-15T10:00:00Z'
  },
  {
    id: '2',
    firstName: 'فاطمة',
    lastName: 'علي',
    email: '<EMAIL>',
    phone: '+966507654321',
    position: 'محاسبة',
    department: 'المالية',
    salary: 6500,
    hireDate: '2023-02-01',
    status: 'active',
    createdAt: '2023-02-01T10:00:00Z',
    updatedAt: '2023-02-01T10:00:00Z'
  },
  {
    id: '3',
    firstName: 'خالد',
    lastName: 'السعد',
    email: '<EMAIL>',
    phone: '+966509876543',
    position: 'مدير مشروع',
    department: 'إدارة المشاريع',
    salary: 12000,
    hireDate: '2022-11-10',
    status: 'active',
    createdAt: '2022-11-10T10:00:00Z',
    updatedAt: '2022-11-10T10:00:00Z'
  },
  {
    id: '4',
    firstName: 'مريم',
    lastName: 'أحمد',
    email: '<EMAIL>',
    phone: '+966502345678',
    position: 'مصممة جرافيك',
    department: 'التسويق',
    salary: 5500,
    hireDate: '2023-03-20',
    status: 'active',
    createdAt: '2023-03-20T10:00:00Z',
    updatedAt: '2023-03-20T10:00:00Z'
  },
  {
    id: '5',
    firstName: 'عبدالله',
    lastName: 'الحربي',
    email: '<EMAIL>',
    phone: '+966508765432',
    position: 'محلل أعمال',
    department: 'تقنية المعلومات',
    salary: 7500,
    hireDate: '2023-04-10',
    status: 'inactive',
    createdAt: '2023-04-10T10:00:00Z',
    updatedAt: '2023-04-10T10:00:00Z'
  }
];

export const useEmployees = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Load employees from localStorage or use mock data
    const savedEmployees = localStorage.getItem('employees');
    if (savedEmployees) {
      setEmployees(JSON.parse(savedEmployees));
    } else {
      setEmployees(mockEmployees);
      localStorage.setItem('employees', JSON.stringify(mockEmployees));
    }
  }, []);

  const saveEmployees = (updatedEmployees: Employee[]) => {
    setEmployees(updatedEmployees);
    localStorage.setItem('employees', JSON.stringify(updatedEmployees));
  };

  const createEmployee = async (employeeData: CreateEmployeeRequest): Promise<Employee> => {
    setLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newEmployee: Employee = {
        ...employeeData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const updatedEmployees = [...employees, newEmployee];
      saveEmployees(updatedEmployees);
      setLoading(false);
      return newEmployee;
    } catch (err) {
      setError('Failed to create employee');
      setLoading(false);
      throw err;
    }
  };

  const updateEmployee = async (employeeData: UpdateEmployeeRequest): Promise<Employee> => {
    setLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const updatedEmployees = employees.map(emp => 
        emp.id === employeeData.id 
          ? { ...emp, ...employeeData, updatedAt: new Date().toISOString() }
          : emp
      );
      
      saveEmployees(updatedEmployees);
      setLoading(false);
      
      const updatedEmployee = updatedEmployees.find(emp => emp.id === employeeData.id);
      if (!updatedEmployee) throw new Error('Employee not found');
      
      return updatedEmployee;
    } catch (err) {
      setError('Failed to update employee');
      setLoading(false);
      throw err;
    }
  };

  const deleteEmployee = async (id: string): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const updatedEmployees = employees.filter(emp => emp.id !== id);
      saveEmployees(updatedEmployees);
      setLoading(false);
    } catch (err) {
      setError('Failed to delete employee');
      setLoading(false);
      throw err;
    }
  };

  const getEmployee = (id: string): Employee | undefined => {
    return employees.find(emp => emp.id === id);
  };

  return {
    employees,
    loading,
    error,
    createEmployee,
    updateEmployee,
    deleteEmployee,
    getEmployee
  };
};

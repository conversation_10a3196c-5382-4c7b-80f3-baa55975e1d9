import { useState, useEffect } from 'react';
import { employee<PERSON><PERSON>, Employee, CreateEmployeeRequest, UpdateEmployeeRequest } from '../api';

export const useEmployees = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadEmployees();
  }, []);

  const loadEmployees = async () => {
    setLoading(true);
    try {
      const response = await employeeAPI.getEmployees();
      if (response.success) {
        setEmployees(response.data);
      }
    } catch (error) {
      console.error('Failed to load employees:', error);
    } finally {
      setLoading(false);
    }
  };

  const createEmployee = async (employeeData: CreateEmployeeRequest): Promise<Employee> => {
    setLoading(true);
    try {
      const response = await employeeAPI.createEmployee(employeeData);
      if (response.success) {
        setEmployees(prev => [...prev, response.data]);
        return response.data;
      }
      throw new Error(response.message);
    } finally {
      setLoading(false);
    }
  };

  const updateEmployee = async (employeeData: UpdateEmployeeRequest): Promise<Employee> => {
    setLoading(true);
    try {
      const response = await employeeAPI.updateEmployee(employeeData);
      if (response.success) {
        setEmployees(prev => prev.map(emp =>
          emp.id === employeeData.id ? response.data : emp
        ));
        return response.data;
      }
      throw new Error(response.message);
    } finally {
      setLoading(false);
    }
  };

  const deleteEmployee = async (id: string): Promise<void> => {
    setLoading(true);
    try {
      const response = await employeeAPI.deleteEmployee(id);
      if (response.success) {
        setEmployees(prev => prev.filter(emp => emp.id !== id));
      } else {
        throw new Error(response.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const getEmployee = (id: string): Employee | undefined => {
    return employees.find(emp => emp.id === id);
  };

  return {
    employees,
    loading,
    createEmployee,
    updateEmployee,
    deleteEmployee,
    getEmployee,
    refreshEmployees: loadEmployees
  };


  const updateEmployee = async (employeeData: UpdateEmployeeRequest): Promise<Employee> => {
    setLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const updatedEmployees = employees.map(emp => 
        emp.id === employeeData.id 
          ? { ...emp, ...employeeData, updatedAt: new Date().toISOString() }
          : emp
      );
      
      saveEmployees(updatedEmployees);
      setLoading(false);
      
      const updatedEmployee = updatedEmployees.find(emp => emp.id === employeeData.id);
      if (!updatedEmployee) throw new Error('Employee not found');
      
      return updatedEmployee;
    } catch (err) {
      setError('Failed to update employee');
      setLoading(false);
      throw err;
    }
  };

  const deleteEmployee = async (id: string): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const updatedEmployees = employees.filter(emp => emp.id !== id);
      saveEmployees(updatedEmployees);
      setLoading(false);
    } catch (err) {
      setError('Failed to delete employee');
      setLoading(false);
      throw err;
    }
  };

  const getEmployee = (id: string): Employee | undefined => {
    return employees.find(emp => emp.id === id);
  };

  return {
    employees,
    loading,
    error,
    createEmployee,
    updateEmployee,
    deleteEmployee,
    getEmployee
  };
};

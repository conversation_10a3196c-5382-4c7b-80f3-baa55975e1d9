{"common": {"login": "<PERSON><PERSON>", "logout": "Logout", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "add": "Add", "search": "Search", "next": "Next", "previous": "Previous", "submit": "Submit", "loading": "Loading...", "error": "Error", "success": "Success", "confirm": "Confirm", "yes": "Yes", "no": "No"}, "auth": {"loginTitle": "<PERSON><PERSON>", "email": "Email", "password": "Password", "emailRequired": "Email is required", "passwordRequired": "Password is required", "invalidCredentials": "Invalid credentials", "loginSuccess": "Login successful"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome", "employeeManagement": "Employee Management"}, "employee": {"title": "Employee Management", "list": "Employee List", "add": "Add Employee", "edit": "Edit Employee", "delete": "Delete Employee", "details": "Employee Details", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "position": "Position", "department": "Department", "salary": "Salary", "hireDate": "Hire Date", "status": "Status", "active": "Active", "inactive": "Inactive", "personalInfo": "Personal Information", "jobInfo": "Job Information", "contactInfo": "Contact Information", "confirmDelete": "Are you sure you want to delete this employee?", "employeeAdded": "Employee added successfully", "employeeUpdated": "Employee updated successfully", "employeeDeleted": "Employee deleted successfully", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailRequired": "Email is required", "phoneRequired": "Phone is required", "positionRequired": "Position is required", "departmentRequired": "Department is required", "salaryRequired": "Salary is required", "hireDateRequired": "Hire date is required"}}
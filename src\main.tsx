import { createRoot } from 'react-dom/client'

// Minimal test without any CSS imports
const root = document.getElementById('root');
if (root) {
  root.innerHTML = '<div style="background: white; color: black; padding: 20px; font-family: Arial;"><h1>React Test</h1><p>If you see this, the basic setup works!</p></div>';
}

// Also try React
createRoot(document.getElementById('root')!).render(
  <div style={{ background: 'white', color: 'black', padding: '20px', fontFamily: 'Arial' }}>
    <h1>React is Working!</h1>
    <p>This is rendered by React</p>
  </div>
)

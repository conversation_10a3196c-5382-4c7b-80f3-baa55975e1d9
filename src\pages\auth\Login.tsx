import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Center,
  FormControl,
  FormLabel,
  Heading,
  Input,
  InputGroup,
  InputRightElement,
  IconButton,
  Text,
  VStack,
  HStack,
  Alert,
  AlertIcon,
  Spinner,
  useColorModeValue,
  Flex,
  Avatar,
  Badge,
} from '@chakra-ui/react';
import { ViewIcon, ViewOffIcon } from '@chakra-ui/icons';
import { useAuth } from '../../context/AuthContext';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const { login, isLoading } = useAuth();
  const { t, i18n } = useTranslation();
  
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const handleChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }));
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!formData.email || !formData.password) {
      setError('Please fill in all fields');
      return;
    }

    try {
      const success = await login(formData.email, formData.password);
      if (success) {
        navigate('/dashboard');
      } else {
        setError('Invalid email or password');
      }
    } catch (error) {
      setError('Login failed. Please try again.');
    }
  };

  const toggleLanguage = () => {
    const newLang = i18n.language === 'ar' ? 'en' : 'ar';
    i18n.changeLanguage(newLang);
  };

  return (
    <Box
      minH="100vh"
      bg={bgColor}
      display="flex"
      alignItems="center"
      justifyContent="center"
      p={4}
    >
      <Box w="full" maxW="md">
        {/* Language Toggle */}
        <Flex justify="flex-end" mb={4}>
          <Button
            variant="ghost"
            size="sm"
            // leftIcon={<LanguageIcon />}
            onClick={toggleLanguage}
            color="primary.500"
          >
            {i18n.language === 'ar' ? 'English' : 'العربية'}
          </Button>
        </Flex>

        <Card
          bg={cardBg}
          borderColor={borderColor}
          borderWidth="1px"
          shadow="xl"
          borderRadius="xl"
        >
          <CardHeader textAlign="center" pb={2}>
            <VStack spacing={4}>
              <Avatar
                size="xl"
                bg="linear-gradient(135deg, rgb(196, 20, 20) 0%, #b71c1c 100%)"
                color="white"
                name="Admin123"
              />
              <Box>
                <Heading
                  size="lg"
                  bgGradient="linear(to-r, primary.500, primary.600)"
                  bgClip="text"
                  fontWeight="bold"
                >
                  Admin123
                </Heading>
                <Text color="gray.600" fontSize="sm">
                  {t('employee.title')}
                </Text>
              </Box>
            </VStack>
          </CardHeader>

          <CardBody pt={2}>
            <form onSubmit={handleSubmit}>
              <VStack spacing={4}>
                {error && (
                  <Alert status="error" borderRadius="md">
                    <AlertIcon />
                    {error}
                  </Alert>
                )}

                <FormControl isRequired>
                  <FormLabel color="gray.700">Email</FormLabel>
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={handleChange('email')}
                    placeholder="<EMAIL>"
                    bg="white"
                    borderColor="gray.300"
                    _hover={{ borderColor: 'primary.400' }}
                    _focus={{ borderColor: 'primary.500', boxShadow: '0 0 0 1px rgb(196, 20, 20)' }}
                  />
                </FormControl>

                <FormControl isRequired>
                  <FormLabel color="gray.700">Password</FormLabel>
                  <InputGroup>
                    <Input
                      type={showPassword ? 'text' : 'password'}
                      value={formData.password}
                      onChange={handleChange('password')}
                      placeholder="Enter your password"
                      bg="white"
                      borderColor="gray.300"
                      _hover={{ borderColor: 'primary.400' }}
                      _focus={{ borderColor: 'primary.500', boxShadow: '0 0 0 1px rgb(196, 20, 20)' }}
                    />
                    <InputRightElement>
                      <IconButton
                        aria-label={showPassword ? 'Hide password' : 'Show password'}
                        icon={showPassword ? <ViewOffIcon /> : <ViewIcon />}
                        onClick={() => setShowPassword(!showPassword)}
                        variant="ghost"
                        size="sm"
                      />
                    </InputRightElement>
                  </InputGroup>
                </FormControl>

                <Button
                  type="submit"
                  colorScheme="primary"
                  size="lg"
                  w="full"
                  isLoading={isLoading}
                  loadingText="Signing in..."
                  bg="linear-gradient(135deg, rgb(196, 20, 20) 0%, #b71c1c 100%)"
                  _hover={{
                    bg: "linear-gradient(135deg, #b71c1c 0%, rgb(196, 20, 20) 100%)",
                    transform: 'translateY(-1px)',
                    boxShadow: '0 8px 25px rgba(196, 20, 20, 0.4)',
                  }}
                  _active={{
                    transform: 'translateY(0)',
                  }}
                >
                  {isLoading ? <Spinner size="sm" /> : 'Sign In'}
                </Button>
              </VStack>
            </form>

            {/* Demo Credentials */}
            <Box
              mt={6}
              p={4}
              bg="linear-gradient(135deg, rgba(196, 20, 20, 0.1) 0%, rgba(183, 28, 28, 0.1) 100%)"
              borderRadius="md"
              border="1px solid"
              borderColor="primary.200"
            >
              <Text fontSize="sm" fontWeight="semibold" color="primary.700" mb={2}>
                Demo Credentials:
              </Text>
              <VStack spacing={1} align="start">
                <HStack>
                  <Text fontSize="xs" color="gray.600">Email:</Text>
                  <Badge colorScheme="primary" fontSize="xs"><EMAIL></Badge>
                </HStack>
                <HStack>
                  <Text fontSize="xs" color="gray.600">Password:</Text>
                  <Badge colorScheme="primary" fontSize="xs">admin123</Badge>
                </HStack>
              </VStack>
            </Box>
          </CardBody>
        </Card>
      </Box>
    </Box>
  );
};

export default Login;

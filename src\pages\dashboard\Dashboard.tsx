import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Paper,
  Avatar,
  Chip,
  LinearProgress,
  Fade,
  Slide
} from '@mui/material';
import {
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  PersonAdd as PersonAddIcon,
  Analytics as AnalyticsIcon,
  WorkOutline as WorkIcon
} from '@mui/icons-material';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const stats = [
    {
      title: t('employee.title'),
      value: '24',
      icon: <PeopleIcon />,
      gradient: 'linear-gradient(135deg, #a6d1e6 0%, #7bb3d3 100%)',
      action: () => navigate('/employees'),
      progress: 85
    },
    {
      title: 'Active Employees',
      value: '22',
      icon: <TrendingUpIcon />,
      gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      progress: 92
    },
    {
      title: 'Departments',
      value: '5',
      icon: <AssignmentIcon />,
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      progress: 100
    }
  ];

  const quickActions = [
    {
      title: t('employee.add'),
      description: 'Add new employee to the system',
      icon: <PersonAddIcon />,
      action: () => navigate('/employees/new'),
      color: '#a6d1e6'
    },
    {
      title: t('employee.list'),
      description: 'View and manage all employees',
      icon: <PeopleIcon />,
      action: () => navigate('/employees'),
      color: '#a6d1e6'
    },
    {
      title: 'Analytics',
      description: 'View detailed reports and analytics',
      icon: <AnalyticsIcon />,
      action: () => {},
      color: '#a6d1e6'
    }
  ];

  return (
    <Box>
      {/* Header */}
      <Fade in={true} timeout={600}>
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h3"
            sx={{
              fontWeight: 800,
              background: 'linear-gradient(135deg, #a6d1e6 0%, #7bb3d3 100%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 1,
            }}
          >
            {t('dashboard.title')}
          </Typography>
          <Typography variant="h6" color="text.secondary">
            {t('dashboard.welcome')} to your employee management system
          </Typography>
        </Box>
      </Fade>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Slide direction="up" in={true} timeout={600 + index * 200}>
              <Card
                sx={{
                  height: '100%',
                  cursor: stat.action ? 'pointer' : 'default',
                  borderRadius: 4,
                  background: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
                  },
                }}
                onClick={stat.action}
                className="hover-lift"
              >
                <CardContent sx={{ p: 3 }}>
                  <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                    <Avatar
                      sx={{
                        width: 60,
                        height: 60,
                        background: stat.gradient,
                      }}
                    >
                      {stat.icon}
                    </Avatar>
                    <Chip
                      label={`${stat.progress}%`}
                      size="small"
                      sx={{
                        background: stat.gradient,
                        color: 'white',
                        fontWeight: 600,
                      }}
                    />
                  </Box>
                  <Typography variant="h3" sx={{ fontWeight: 800, mb: 1 }}>
                    {stat.value}
                  </Typography>
                  <Typography color="text.secondary" variant="body1" sx={{ mb: 2 }}>
                    {stat.title}
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={stat.progress}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      background: 'rgba(0, 0, 0, 0.1)',
                      '& .MuiLinearProgress-bar': {
                        background: stat.gradient,
                        borderRadius: 3,
                      },
                    }}
                  />
                </CardContent>
              </Card>
            </Slide>
          </Grid>
        ))}
      </Grid>

      {/* Quick Actions */}
      <Fade in={true} timeout={1000}>
        <Paper
          sx={{
            p: 4,
            borderRadius: 4,
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
          }}
        >
          <Typography variant="h5" sx={{ fontWeight: 700, mb: 3 }}>
            🚀 Quick Actions
          </Typography>
          <Grid container spacing={3}>
            {quickActions.map((action, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    borderRadius: 3,
                    border: '2px solid transparent',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      borderColor: action.color,
                      transform: 'translateY(-4px)',
                      boxShadow: `0 12px 24px ${action.color}20`,
                    },
                  }}
                  onClick={action.action}
                >
                  <CardContent sx={{ p: 3, textAlign: 'center' }}>
                    <Avatar
                      sx={{
                        width: 50,
                        height: 50,
                        mx: 'auto',
                        mb: 2,
                        background: `${action.color}20`,
                        color: action.color,
                      }}
                    >
                      {action.icon}
                    </Avatar>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      {action.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {action.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Paper>
      </Fade>
    </Box>
  );
};

export default Dashboard;

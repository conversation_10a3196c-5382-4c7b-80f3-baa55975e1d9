import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Text,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Progress,
  Icon,
  VStack,
  HStack,
  Avatar,
  Badge,
  Button,
  Flex,
  useColorModeValue,
  Fade,
  ScaleFade,
} from '@chakra-ui/react';
import { 
  FiUsers, 
  FiTrendingUp, 
  FiBriefcase, 
  FiUserPlus, 
  // FiBarChart3 
} from 'react-icons/fi';
import { dashboardAPI } from '../../api';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalEmployees: 0,
    activeEmployees: 0,
    departments: 0,
    recentHires: 0
  });
  const [loading, setLoading] = useState(true);

  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const response = await dashboardAPI.getStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('Failed to load stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const statsCards = [
    {
      title: t('employee.title'),
      value: stats.totalEmployees.toString(),
      icon: FiUsers,
      color: 'primary',
      bgGradient: 'linear(to-r, primary.500, primary.600)',
      action: () => navigate('/employees'),
      progress: Math.min((stats.totalEmployees / 50) * 100, 100),
      helpText: 'Total employees in system'
    },
    {
      title: 'Active Employees',
      value: stats.activeEmployees.toString(),
      icon: FiTrendingUp,
      color: 'success',
      bgGradient: 'linear(to-r, success.500, success.600)',
      progress: stats.totalEmployees > 0 ? Math.round((stats.activeEmployees / stats.totalEmployees) * 100) : 0,
      helpText: 'Currently active employees'
    },
    {
      title: 'Departments',
      value: stats.departments.toString(),
      icon: FiBriefcase,
      color: 'secondary',
      bgGradient: 'linear(to-r, secondary.500, secondary.600)',
      progress: Math.min((stats.departments / 10) * 100, 100),
      helpText: 'Active departments'
    }
  ];

  const quickActions = [
    {
      title: t('employee.add'),
      description: 'Add new employee to the system',
      icon: FiUserPlus,
      action: () => navigate('/employees/new'),
      color: 'secondary.500',
      bgColor: 'secondary.50'
    },
    {
      title: t('employee.list'),
      description: 'View and manage all employees',
      icon: FiUsers,
      action: () => navigate('/employees'),
      color: 'primary.500',
      bgColor: 'primary.50'
    },
    {
      title: 'Analytics',
      description: 'View detailed reports and analytics',
      icon: FiUserPlus,
      action: () => {},
      color: 'success.500',
      bgColor: 'success.50'
    }
  ];

  return (
    <Box p={6}>
      {/* Header */}
      <Fade in={true}>
        <Box mb={8}>
          <Heading
            size="xl"
            color="primary.500"
            mb={2}
          >
            {t('dashboard.title')}
          </Heading>
          <Text color="gray.600" fontSize="lg">
            {t('dashboard.welcome')} to your employee management system
          </Text>
        </Box>
      </Fade>

      {/* Stats Cards */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6} mb={8}>
        {statsCards.map((stat, index) => (
          <ScaleFade key={index} in={!loading} delay={index * 0.1}>
            <Card
              bg={cardBg}
              borderColor={borderColor}
              borderWidth="1px"
              cursor={stat.action ? 'pointer' : 'default'}
              transition="all 0.2s"
              _hover={stat.action ? {
                transform: 'translateY(-2px)',
                boxShadow: 'xl',
                borderColor: stat.color + '.300'
              } : {}}
              onClick={stat.action}
            >
              <CardBody>
                <Flex justify="space-between" align="start" mb={4}>
                  <Box>
                    <Stat>
                      <StatLabel color="gray.600" fontSize="sm">
                        {stat.title}
                      </StatLabel>
                      <StatNumber fontSize="3xl" fontWeight="bold" color="gray.800">
                        {stat.value}
                      </StatNumber>
                      <StatHelpText color="gray.500" fontSize="xs">
                        {stat.helpText}
                      </StatHelpText>
                    </Stat>
                  </Box>
                  <Avatar
                    size="md"
                    bg={stat.bgGradient}
                    icon={<Icon as={stat.icon} boxSize={6} color="white" />}
                  />
                </Flex>
                
                <Box>
                  <Flex justify="space-between" align="center" mb={2}>
                    <Text fontSize="xs" color="gray.500">Progress</Text>
                    <Badge colorScheme={stat.color} fontSize="xs">
                      {stat.progress}%
                    </Badge>
                  </Flex>
                  <Progress
                    value={stat.progress}
                    colorScheme={stat.color}
                    size="sm"
                    borderRadius="full"
                  />
                </Box>
              </CardBody>
            </Card>
          </ScaleFade>
        ))}
      </SimpleGrid>

      {/* Quick Actions */}
      <Fade in={true} delay={0.3}>
        <Card bg={cardBg} borderColor={borderColor} borderWidth="1px">
          <CardHeader>
            <Heading size="md" color="gray.800">
              🚀 Quick Actions
            </Heading>
          </CardHeader>
          <CardBody pt={0}>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
              {quickActions.map((action, index) => (
                <Card
                  key={index}
                  cursor="pointer"
                  borderWidth="1px"
                  borderColor="gray.200"
                  transition="all 0.2s"
                  _hover={{
                    borderColor: action.color,
                    transform: 'translateY(-2px)',
                    boxShadow: 'md',
                  }}
                  onClick={action.action}
                >
                  <CardBody textAlign="center">
                    <VStack spacing={3}>
                      <Avatar
                        size="md"
                        bg={action.bgColor}
                        color={action.color}
                        icon={<Icon as={action.icon} boxSize={5} />}
                      />
                      <Box>
                        <Heading size="sm" color="gray.800" mb={1}>
                          {action.title}
                        </Heading>
                        <Text fontSize="sm" color="gray.600">
                          {action.description}
                        </Text>
                      </Box>
                    </VStack>
                  </CardBody>
                </Card>
              ))}
            </SimpleGrid>
          </CardBody>
        </Card>
      </Fade>
    </Box>
  );
};

export default Dashboard;

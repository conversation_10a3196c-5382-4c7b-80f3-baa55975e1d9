import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Paper,
  Avatar,
  Chip,
  LinearProgress,
  Fade,
  Slide
} from '@mui/material';
import {
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  PersonAdd as PersonAddIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const stats = [
    {
      title: t('employee.title'),
      value: '24',
      icon: <PeopleIcon />,
      gradient: 'linear-gradient(135deg, rgb(196, 20, 20) 0%, #b71c1c 100%)',
      action: () => navigate('/employees'),
      progress: 85
    },
    {
      title: 'Active Employees',
      value: '22',
      icon: <TrendingUpIcon />,
      gradient: 'linear-gradient(135deg, #14b8a6 0%, #047857 100%)',
      progress: 92
    },
    {
      title: 'Departments',
      value: '5',
      icon: <AssignmentIcon />,
      gradient: 'linear-gradient(135deg, #ca8a04 0%, #92400e 100%)',
      progress: 100
    }
  ];

  const quickActions = [
    {
      title: t('employee.add'),
      description: 'Add new employee to the system',
      icon: <PersonAddIcon />,
      action: () => navigate('/employees/new'),
      color: '#ca8a04'
    },
    {
      title: t('employee.list'),
      description: 'View and manage all employees',
      icon: <PeopleIcon />,
      action: () => navigate('/employees'),
      color: 'rgb(196, 20, 20)'
    },
    {
      title: 'Analytics',
      description: 'View detailed reports and analytics',
      icon: <AnalyticsIcon />,
      action: () => {},
      color: '#14b8a6'
    }
  ];

  return (
    <Box>
      {/* Header */}
      <Fade in={true} timeout={600}>
        <Box sx={{ mb: 3 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              color: 'rgb(196, 20, 20)',
              mb: 0.5,
            }}
          >
            {t('dashboard.title')}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {t('dashboard.welcome')} to your employee management system
          </Typography>
        </Box>
      </Fade>

      {/* Stats Cards */}
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' },
          gap: 2,
          mb: 3
        }}
      >
        {stats.map((stat, index) => (
          <Slide direction="up" in={true} timeout={600 + index * 200} key={index}>
            <Card
              sx={{
                cursor: stat.action ? 'pointer' : 'default',
                borderRadius: 2,
                background: 'white',
                border: '1px solid rgba(0, 0, 0, 0.08)',
                transition: 'all 0.2s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
                },
              }}
              onClick={stat.action}
            >
              <CardContent sx={{ p: 2.5 }}>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={1.5}>
                  <Avatar
                    sx={{
                      width: 48,
                      height: 48,
                      background: stat.gradient,
                    }}
                  >
                    {stat.icon}
                  </Avatar>
                  <Chip
                    label={`${stat.progress}%`}
                    size="small"
                    sx={{
                      background: stat.gradient,
                      color: 'white',
                      fontWeight: 600,
                      fontSize: '0.75rem',
                      height: 24,
                    }}
                  />
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
                  {stat.value}
                </Typography>
                <Typography color="text.secondary" variant="body2" sx={{ mb: 1.5 }}>
                  {stat.title}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={stat.progress}
                  sx={{
                    height: 4,
                    borderRadius: 2,
                    background: 'rgba(0, 0, 0, 0.08)',
                    '& .MuiLinearProgress-bar': {
                      background: stat.gradient,
                      borderRadius: 2,
                    },
                  }}
                />
              </CardContent>
            </Card>
          </Slide>
        ))}
      </Box>

      {/* Quick Actions */}
      <Fade in={true} timeout={1000}>
        <Paper
          sx={{
            p: 3,
            borderRadius: 2,
            background: 'white',
            border: '1px solid rgba(0, 0, 0, 0.08)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
          }}
        >
          <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
            🚀 Quick Actions
          </Typography>
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' },
              gap: 2
            }}
          >
            {quickActions.map((action, index) => (
              <Card
                key={index}
                sx={{
                  cursor: 'pointer',
                  borderRadius: 2,
                  border: '1px solid rgba(0, 0, 0, 0.08)',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    borderColor: action.color,
                    transform: 'translateY(-2px)',
                    boxShadow: `0 8px 16px ${action.color}20`,
                  },
                }}
                onClick={action.action}
              >
                <CardContent sx={{ p: 2.5, textAlign: 'center' }}>
                  <Avatar
                    sx={{
                      width: 40,
                      height: 40,
                      mx: 'auto',
                      mb: 1.5,
                      background: `${action.color}15`,
                      color: action.color,
                    }}
                  >
                    {action.icon}
                  </Avatar>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1rem' }}>
                    {action.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8125rem' }}>
                    {action.description}
                  </Typography>
                </CardContent>
              </Card>
            ))}
          </Box>
        </Paper>
      </Fade>
    </Box>
  );
};

export default Dashboard;

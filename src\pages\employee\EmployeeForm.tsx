import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Button,
  Typography,
  Alert,
  Snackbar,
  CircularProgress
} from '@mui/material';
import { useEmployees } from '../../hooks/useEmployees';
import PersonalInfoStep from '../../components/employee/PersonalInfoStep';
import JobInfoStep from '../../components/employee/JobInfoStep';
import ReviewStep from '../../components/employee/ReviewStep';

const EmployeeForm: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { createEmployee, updateEmployee, getEmployee, loading } = useEmployees();

  const [activeStep, setActiveStep] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    position: '',
    department: '',
    salary: '',
    hireDate: '',
    status: 'active'
  });
  const [errors, setErrors] = useState<any>({});

  const isEdit = Boolean(id);

  useEffect(() => {
    if (isEdit && id) {
      const employee = getEmployee(id);
      if (employee) {
        setFormData({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          phone: employee.phone,
          position: employee.position,
          department: employee.department,
          salary: employee.salary.toString(),
          hireDate: employee.hireDate,
          status: employee.status
        });
      }
    }
  }, [id, isEdit, getEmployee]);

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev: any) => ({ ...prev, [field]: null }));
    }
  };

  const validateStep = (step: number) => {
    const newErrors: any = {};

    if (step === 0) {
      if (!formData.firstName) newErrors.firstName = { message: t('employee.firstNameRequired') };
      if (!formData.lastName) newErrors.lastName = { message: t('employee.lastNameRequired') };
      if (!formData.email) newErrors.email = { message: t('employee.emailRequired') };
      if (!formData.phone) newErrors.phone = { message: t('employee.phoneRequired') };
    } else if (step === 1) {
      if (!formData.position) newErrors.position = { message: t('employee.positionRequired') };
      if (!formData.department) newErrors.department = { message: t('employee.departmentRequired') };
      if (!formData.salary) newErrors.salary = { message: t('employee.salaryRequired') };
      if (!formData.hireDate) newErrors.hireDate = { message: t('employee.hireDateRequired') };
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const steps = [
    t('employee.personalInfo'),
    t('employee.jobInfo'),
    t('common.confirm')
  ];

  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return <PersonalInfoStep formData={formData} onChange={handleChange} errors={errors} />;
      case 1:
        return <JobInfoStep formData={formData} onChange={handleChange} errors={errors} />;
      case 2:
        return <ReviewStep formData={formData} />;
      default:
        return 'Unknown step';
    }
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleSubmit = async () => {
    try {
      const employeeData = {
        ...formData,
        salary: parseFloat(formData.salary)
      };

      if (isEdit && id) {
        await updateEmployee({ id, ...employeeData });
        setSnackbar({
          open: true,
          message: t('employee.employeeUpdated'),
          severity: 'success'
        });
      } else {
        await createEmployee(employeeData);
        setSnackbar({
          open: true,
          message: t('employee.employeeAdded'),
          severity: 'success'
        });
      }

      setTimeout(() => {
        navigate('/employees');
      }, 2000);
    } catch (error) {
      setSnackbar({
        open: true,
        message: t('common.error'),
        severity: 'error'
      });
    }
  };

  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {isEdit ? t('employee.edit') : t('employee.add')}
      </Typography>

      <Paper sx={{ p: 3 }}>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        <div>
          {getStepContent(activeStep)}

          <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
            <Button
              color="inherit"
              disabled={activeStep === 0}
              onClick={handleBack}
              sx={{ mr: 1 }}
            >
              {t('common.previous')}
            </Button>
            <Box sx={{ flex: '1 1 auto' }} />
            {activeStep === steps.length - 1 ? (
              <Button
                onClick={handleSubmit}
                variant="contained"
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : null}
              >
                {loading ? t('common.loading') : t('common.save')}
              </Button>
            ) : (
              <Button onClick={handleNext} variant="contained">
                {t('common.next')}
              </Button>
            )}
          </Box>
        </div>
      </Paper>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default EmployeeForm;

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
  Box,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Button,
  Typography,
  Alert,
  Snackbar,
  CircularProgress
} from '@mui/material';
import { useEmployees } from '../../hooks/useEmployees';
import PersonalInfoStep from '../../components/employee/PersonalInfoStep';
import JobInfoStep from '../../components/employee/JobInfoStep';
import ReviewStep from '../../components/employee/ReviewStep';

interface EmployeeFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  department: string;
  salary: string;
  hireDate: string;
  status: 'active' | 'inactive';
}

const EmployeeForm: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { createEmployee, updateEmployee, getEmployee, loading } = useEmployees();
  
  const [activeStep, setActiveStep] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  const isEdit = Boolean(id);

  const schema = yup.object({
    firstName: yup.string().required(t('employee.firstNameRequired')),
    lastName: yup.string().required(t('employee.lastNameRequired')),
    email: yup.string().email().required(t('employee.emailRequired')),
    phone: yup.string().required(t('employee.phoneRequired')),
    position: yup.string().required(t('employee.positionRequired')),
    department: yup.string().required(t('employee.departmentRequired')),
    salary: yup.string().required(t('employee.salaryRequired')),
    hireDate: yup.string().required(t('employee.hireDateRequired')),
    status: yup.string().oneOf(['active', 'inactive']).required()
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
    trigger
  } = useForm<EmployeeFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      position: '',
      department: '',
      salary: '',
      hireDate: '',
      status: 'active'
    }
  });

  useEffect(() => {
    if (isEdit && id) {
      const employee = getEmployee(id);
      if (employee) {
        reset({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          phone: employee.phone,
          position: employee.position,
          department: employee.department,
          salary: employee.salary.toString(),
          hireDate: employee.hireDate,
          status: employee.status
        });
      }
    }
  }, [id, isEdit, getEmployee, reset]);

  const steps = [
    t('employee.personalInfo'),
    t('employee.jobInfo'),
    t('common.confirm')
  ];

  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return <PersonalInfoStep control={control} errors={errors} />;
      case 1:
        return <JobInfoStep control={control} errors={errors} />;
      case 2:
        return <ReviewStep formData={watch()} />;
      default:
        return 'Unknown step';
    }
  };

  const handleNext = async () => {
    let fieldsToValidate: (keyof EmployeeFormData)[] = [];
    
    if (activeStep === 0) {
      fieldsToValidate = ['firstName', 'lastName', 'email', 'phone', 'status'];
    } else if (activeStep === 1) {
      fieldsToValidate = ['position', 'department', 'salary', 'hireDate'];
    }

    const isStepValid = await trigger(fieldsToValidate);
    
    if (isStepValid) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const onSubmit = async (data: EmployeeFormData) => {
    try {
      const employeeData = {
        ...data,
        salary: parseFloat(data.salary)
      };

      if (isEdit && id) {
        await updateEmployee({ id, ...employeeData });
        setSnackbar({
          open: true,
          message: t('employee.employeeUpdated'),
          severity: 'success'
        });
      } else {
        await createEmployee(employeeData);
        setSnackbar({
          open: true,
          message: t('employee.employeeAdded'),
          severity: 'success'
        });
      }
      
      setTimeout(() => {
        navigate('/employees');
      }, 2000);
    } catch (error) {
      setSnackbar({
        open: true,
        message: t('common.error'),
        severity: 'error'
      });
    }
  };

  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {isEdit ? t('employee.edit') : t('employee.add')}
      </Typography>

      <Paper sx={{ p: 3 }}>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        <form onSubmit={handleSubmit(onSubmit)}>
          {getStepContent(activeStep)}

          <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
            <Button
              color="inherit"
              disabled={activeStep === 0}
              onClick={handleBack}
              sx={{ mr: 1 }}
            >
              {t('common.previous')}
            </Button>
            <Box sx={{ flex: '1 1 auto' }} />
            {activeStep === steps.length - 1 ? (
              <Button 
                type="submit" 
                variant="contained"
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : null}
              >
                {loading ? t('common.loading') : t('common.save')}
              </Button>
            ) : (
              <Button onClick={handleNext} variant="contained">
                {t('common.next')}
              </Button>
            )}
          </Box>
        </form>
      </Paper>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default EmployeeForm;

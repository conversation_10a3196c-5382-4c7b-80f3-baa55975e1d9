import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Define translations inline to avoid import issues
const arTranslations = {
  "common": {
    "login": "تسجيل الدخول",
    "logout": "تسجيل الخروج",
    "save": "حفظ",
    "cancel": "إلغاء",
    "edit": "تعديل",
    "delete": "حذف",
    "add": "إضافة",
    "search": "بحث",
    "next": "التالي",
    "previous": "السابق",
    "submit": "إرسال",
    "loading": "جاري التحميل...",
    "error": "خطأ",
    "success": "نجح",
    "confirm": "تأكيد",
    "yes": "نعم",
    "no": "لا"
  },
  "auth": {
    "loginTitle": "تسجيل الدخول",
    "email": "البريد الإلكتروني",
    "password": "كلمة المرور",
    "emailRequired": "البريد الإلكتروني مطلوب",
    "passwordRequired": "كلمة المرور مطلوبة",
    "invalidCredentials": "بيانات الدخول غير صحيحة",
    "loginSuccess": "تم تسجيل الدخول بنجاح"
  },
  "dashboard": {
    "title": "لوحة التحكم",
    "welcome": "مرحباً",
    "employeeManagement": "إدارة الموظفين"
  },
  "employee": {
    "title": "إدارة الموظفين",
    "list": "قائمة الموظفين",
    "add": "إضافة موظف",
    "edit": "تعديل موظف",
    "delete": "حذف موظف",
    "details": "تفاصيل الموظف",
    "firstName": "الاسم الأول",
    "lastName": "اسم العائلة",
    "email": "البريد الإلكتروني",
    "phone": "رقم الهاتف",
    "position": "المنصب",
    "department": "القسم",
    "salary": "الراتب",
    "hireDate": "تاريخ التوظيف",
    "status": "الحالة",
    "active": "نشط",
    "inactive": "غير نشط",
    "personalInfo": "المعلومات الشخصية",
    "jobInfo": "معلومات الوظيفة",
    "contactInfo": "معلومات الاتصال",
    "confirmDelete": "هل أنت متأكد من حذف هذا الموظف؟",
    "employeeAdded": "تم إضافة الموظف بنجاح",
    "employeeUpdated": "تم تحديث الموظف بنجاح",
    "employeeDeleted": "تم حذف الموظف بنجاح",
    "firstNameRequired": "الاسم الأول مطلوب",
    "lastNameRequired": "اسم العائلة مطلوب",
    "emailRequired": "البريد الإلكتروني مطلوب",
    "phoneRequired": "رقم الهاتف مطلوب",
    "positionRequired": "المنصب مطلوب",
    "departmentRequired": "القسم مطلوب",
    "salaryRequired": "الراتب مطلوب",
    "hireDateRequired": "تاريخ التوظيف مطلوب"
  }
};

const enTranslations = {
  "common": {
    "login": "Login",
    "logout": "Logout",
    "save": "Save",
    "cancel": "Cancel",
    "edit": "Edit",
    "delete": "Delete",
    "add": "Add",
    "search": "Search",
    "next": "Next",
    "previous": "Previous",
    "submit": "Submit",
    "loading": "Loading...",
    "error": "Error",
    "success": "Success",
    "confirm": "Confirm",
    "yes": "Yes",
    "no": "No"
  },
  "auth": {
    "loginTitle": "Login",
    "email": "Email",
    "password": "Password",
    "emailRequired": "Email is required",
    "passwordRequired": "Password is required",
    "invalidCredentials": "Invalid credentials",
    "loginSuccess": "Login successful"
  },
  "dashboard": {
    "title": "Dashboard",
    "welcome": "Welcome",
    "employeeManagement": "Employee Management"
  },
  "employee": {
    "title": "Employee Management",
    "list": "Employee List",
    "add": "Add Employee",
    "edit": "Edit Employee",
    "delete": "Delete Employee",
    "details": "Employee Details",
    "firstName": "First Name",
    "lastName": "Last Name",
    "email": "Email",
    "phone": "Phone",
    "position": "Position",
    "department": "Department",
    "salary": "Salary",
    "hireDate": "Hire Date",
    "status": "Status",
    "active": "Active",
    "inactive": "Inactive",
    "personalInfo": "Personal Information",
    "jobInfo": "Job Information",
    "contactInfo": "Contact Information",
    "confirmDelete": "Are you sure you want to delete this employee?",
    "employeeAdded": "Employee added successfully",
    "employeeUpdated": "Employee updated successfully",
    "employeeDeleted": "Employee deleted successfully",
    "firstNameRequired": "First name is required",
    "lastNameRequired": "Last name is required",
    "emailRequired": "Email is required",
    "phoneRequired": "Phone is required",
    "positionRequired": "Position is required",
    "departmentRequired": "Department is required",
    "salaryRequired": "Salary is required",
    "hireDateRequired": "Hire date is required"
  }
};

const resources = {
  ar: {
    translation: arTranslations,
  },
  en: {
    translation: enTranslations,
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    lng: 'ar', // Default language is Arabic
    fallbackLng: 'ar',
    debug: false,

    interpolation: {
      escapeValue: false, // React already does escaping
    },

    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },
  });

export default i18n;
